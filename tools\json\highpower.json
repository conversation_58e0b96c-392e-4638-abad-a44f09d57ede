{"豪鹏电池寿命预测": {"categories": [{"name": "基础组件", "nodes": [{"data": {"category": "materialDesign", "description": "", "icon": {"type": "svg", "value": "svg.batteryDataInput"}, "inputType": [], "label": "豪鹏电芯数据输入", "nodeType": "Basic", "outputType": ["HighpowerBatteryModelCalibration"], "params": {}, "type": "HighpowerCellDataInput"}, "id": "1", "type": "custom"}, {"data": {"category": "materialDesign", "description": "", "icon": {"type": "svg", "value": "svg.batteryModel"}, "inputType": ["HighpowerBatteryModelCalibration"], "label": "豪鹏电池模型", "nodeType": "Basic", "outputType": ["HighpowerCellLifePrediction"], "params": {}, "type": "HighpowerBatteryModel"}, "id": "1", "type": "custom"}]}, {"name": "计算组件", "nodes": [{"data": {"category": "materialDesign", "description": "", "icon": {"type": "svg", "value": "svg.batteryCalibration"}, "label": "豪鹏电池模型标定", "nodeType": "Compute", "inputType": ["HighpowerCellDataInput"], "outputType": ["HighpowerBatteryModel"], "params": {}, "type": "HighpowerBatteryModelCalibration"}, "id": "3", "type": "custom"}, {"data": {"category": "materialDesign", "description": "", "icon": {"type": "svg", "value": "svg.lifePrediction"}, "inputType": ["DataEntry", "HighpowerBatteryModel", "TrainInputData", "TrainModelConfig"], "label": "豪鹏电芯寿命预测", "nodeType": "Compute", "outputType": [], "params": {}, "type": "HighpowerCellLifePrediction"}, "id": "2", "type": "custom"}, {"data": {"category": "materialDesign", "description": "", "icon": {"type": "svg", "value": "svg.ModelTrain"}, "inputType": ["TrainInputData"], "label": "机器学习模型训练", "nodeType": "Compute", "outputType": ["TrainModelConfig"], "params": {}, "type": "ModelTrain"}, "id": "3", "type": "custom"}]}, {"name": "学习组件", "nodes": [{"data": {"category": "materialDesign", "description": "", "icon": {"type": "svg", "value": "svg.StudyInputData"}, "inputType": [], "label": "数据集导入", "nodeType": "Train", "outputType": ["HighpowerCellLifePrediction"], "params": {}, "type": "TrainInputData"}, "id": "1", "type": "custom"}, {"data": {"category": "materialDesign", "description": "", "icon": {"type": "svg", "value": "svg.StudyModelConfig"}, "inputType": ["ModelTrain"], "label": "豪鹏机器学习模型", "nodeType": "Train", "outputType": ["HighpowerCellLifePrediction"], "params": {}, "type": "TrainModelConfig"}, "id": "2", "type": "custom"}]}], "description": "豪鹏电池寿命预测管理模块", "icon": {"type": "icon", "value": "material-symbols:battery-charging-full-sharp"}, "type": "HighpowerLifePrediction"}}